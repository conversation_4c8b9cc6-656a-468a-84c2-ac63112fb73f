import requests
import re
import json
import sys
from datetime import datetime


def get_steam_app_info(app_id):
    """
    获取Steam应用信息

    Args:
        app_id (str): Steam应用ID

    Returns:
        str: API响应文本
    """
    url = f"https://steamui.com/api/get_appinfo.php?appid={app_id}"

    try:
        print(f"正在请求: {url}")
        response = requests.get(url, timeout=30)
        response.raise_for_status()  # 检查HTTP错误
        print(f"响应状态码: {response.status_code}")
        return response.text
    except requests.exceptions.RequestException as e:
        print(f"请求失败: {e}")
        return None


def extract_depot_manifest_info(text):
    """
    使用正则表达式提取depot和manifest信息
    
    Args:
        text (str): API响应文本
        
    Returns:
        list: 包含(depot_id, gid)元组的列表
    """
    # 正则表达式模式：匹配depot ID和对应的gid
    # 匹配模式：数字ID -> manifests -> public -> gid -> 数字值
    pattern = r'"(\d+)"\s*\{\s*(?:"dlcappid"\s*"\d+"\s*)?(?:"config"[^}]*\}\s*)?(?:"depotfromapp"[^}]*)?(?:"sharedinstall"[^}]*)?(?:"manifests"\s*\{[^}]*"public"\s*\{\s*"gid"\s*"(\d+)")'
    
    matches = re.findall(pattern, text, re.DOTALL)
    
    # 过滤掉非数字开头的depot（如"branches"等）
    filtered_matches = []
    for depot_id, gid in matches:
        # 确保depot_id是纯数字且长度合理（Steam depot ID通常是7-8位数字）
        if depot_id.isdigit() and len(depot_id) >= 6:
            filtered_matches.append((depot_id, gid))
    
    return filtered_matches


def save_results_to_file(app_id, depot_manifest_pairs, app_name="未知应用"):
    """
    保存结果到文件

    Args:
        app_id (str): Steam应用ID
        depot_manifest_pairs (list): depot和manifest对列表
        app_name (str): 应用名称
    """
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"Steam提取结果_{app_id}_{timestamp}.txt"

    with open(filename, "w", encoding="utf-8") as f:
        f.write(f"Steam应用信息提取结果\n")
        f.write(f"=" * 50 + "\n")
        f.write(f"应用ID: {app_id}\n")
        f.write(f"应用名称: {app_name}\n")
        f.write(f"提取时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"总计depot数量: {len(depot_manifest_pairs)}\n\n")

        f.write("Depot ID 和 Manifest GID 对应关系:\n")
        f.write("-" * 50 + "\n")
        for depot_id, gid in depot_manifest_pairs:
            f.write(f"{depot_id},{gid}\n")

    print(f"结果已保存到: {filename}")


def extract_app_name(text):
    """
    从API响应中提取应用名称

    Args:
        text (str): API响应文本

    Returns:
        str: 应用名称
    """
    name_pattern = r'"name"\s*"([^"]+)"'
    match = re.search(name_pattern, text)
    return match.group(1) if match else "未知应用"


def process_multiple_apps(app_ids):
    """
    批量处理多个Steam应用

    Args:
        app_ids (list): Steam应用ID列表
    """
    all_results = {}

    for app_id in app_ids:
        print(f"\n{'='*60}")
        print(f"正在处理应用ID: {app_id}")
        print(f"{'='*60}")

        response_text = get_steam_app_info(app_id)
        if response_text is None:
            print(f"跳过应用 {app_id}（获取失败）")
            continue

        app_name = extract_app_name(response_text)
        depot_manifest_pairs = extract_depot_manifest_info(response_text)

        all_results[app_id] = {
            'name': app_name,
            'pairs': depot_manifest_pairs
        }

        print(f"应用名称: {app_name}")
        if depot_manifest_pairs:
            print(f"找到 {len(depot_manifest_pairs)} 个depot-manifest对:")
            for depot_id, gid in depot_manifest_pairs:
                print(f"  {depot_id} → {gid}")

            # 保存单个应用结果
            save_results_to_file(app_id, depot_manifest_pairs, app_name)
        else:
            print("未找到depot-manifest信息")

    return all_results


def main():
    """主函数"""
    print("Steam Depot和Manifest信息提取器")
    print("=" * 50)

    # 检查命令行参数
    if len(sys.argv) > 1:
        # 从命令行参数获取应用ID
        app_ids = sys.argv[1:]
        print(f"从命令行参数获取到 {len(app_ids)} 个应用ID")
    else:
        # 交互式输入
        print("请输入Steam应用ID（多个ID用空格分隔）:")
        user_input = input().strip()
        if not user_input:
            # 默认使用示例ID
            app_ids = ["3489700"]
            print("使用默认应用ID: 3489700")
        else:
            app_ids = user_input.split()

    # 验证应用ID格式
    valid_app_ids = []
    for app_id in app_ids:
        if app_id.isdigit():
            valid_app_ids.append(app_id)
        else:
            print(f"警告: 跳过无效的应用ID '{app_id}'")

    if not valid_app_ids:
        print("错误: 没有有效的应用ID")
        return

    # 处理应用
    if len(valid_app_ids) == 1:
        # 单个应用处理
        app_id = valid_app_ids[0]
        response_text = get_steam_app_info(app_id)

        if response_text is None:
            print("无法获取数据，程序退出")
            return

        app_name = extract_app_name(response_text)
        depot_manifest_pairs = extract_depot_manifest_info(response_text)

        print(f"\n应用名称: {app_name}")

        if depot_manifest_pairs:
            print("\n提取到的Depot和Manifest信息：")
            print("-" * 50)
            for depot_id, gid in depot_manifest_pairs:
                print(f"Depot ID: {depot_id}, Manifest GID: {gid}")

            print(f"\n总共找到 {len(depot_manifest_pairs)} 个depot-manifest对")
            save_results_to_file(app_id, depot_manifest_pairs, app_name)
        else:
            print("未找到匹配的depot和manifest信息")

        # 保存原始响应
        with open(f"原始响应_{app_id}.txt", "w", encoding="utf-8") as f:
            f.write(response_text)
        print(f"\n原始API响应已保存到 '原始响应_{app_id}.txt'")

    else:
        # 批量处理
        print(f"\n开始批量处理 {len(valid_app_ids)} 个应用...")
        results = process_multiple_apps(valid_app_ids)

        # 生成汇总报告
        print(f"\n{'='*60}")
        print("批量处理完成 - 汇总报告")
        print(f"{'='*60}")

        total_depots = 0
        for app_id, data in results.items():
            depot_count = len(data['pairs'])
            total_depots += depot_count
            print(f"{app_id} ({data['name']}): {depot_count} 个depot")

        print(f"\n总计处理了 {len(results)} 个应用，找到 {total_depots} 个depot")


if __name__ == "__main__":
    main()
